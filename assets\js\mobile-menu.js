
(function($){
    'use script';
/*---canvas menu activation---*/
    $(".canvas_open, .off_canvars_overlay, .canvas_close, .canvas_open").click(function () {
        if ($('.offcanvas_menu_wrapper, .off_canvars_overlay, .canvas_open').hasClass("active")) {
            $(".offcanvas_menu_wrapper, .canvas_close, .off_canvars_overlay, .canvas_open").removeClass("active");
        }
        else {
            $(".offcanvas_menu_wrapper, .off_canvars_overlay, .canvas_open").removeClass("active");
            $('.offcanvas_menu_wrapper, .canvas_close, .off_canvars_overlay, .canvas_open').addClass("active");
        }
    });


	/*---Off Canvas Menu---*/
    var $offcanvasNav = $('.offcanvas_main_menu'),
        $offcanvasNavSubMenu = $offcanvasNav.find('.sub-menu');
    $offcanvasNavSubMenu.parent().prepend('<span class="menu-expand"><i class="bi bi-chevron-down"></i></span>');
    
    $offcanvasNavSubMenu.slideUp();
    
    $offcanvasNav.on('click', 'li a, li .menu-expand', function(e) {
        var $this = $(this);
        if ( ($this.parent().attr('class').match(/\b(menu-item-has-children|has-children|has-sub-menu)\b/)) && ($this.attr('href') === '#' || $this.hasClass('menu-expand')) ) {
            e.preventDefault();
            if ($this.siblings('ul:visible').length){
                $this.siblings('ul').slideUp('slow');
            }else {
                $this.closest('li').siblings('li').find('ul:visible').slideUp('slow');
                $this.siblings('ul').slideDown('slow');
            }
        }
        if( $this.is('a') || $this.is('span') || $this.attr('clas').match(/\b(menu-expand)\b/) ){
        	$this.parent().toggleClass('menu-open');
        }else if( $this.is('li') && $this.attr('class').match(/\b('menu-item-has-children')\b/) ){
        	$this.toggleClass('menu-open');
        }
    });
}(jQuery));